#!/usr/bin/env python3
"""
Integration test to verify ORB conditions work in the full backtesting pipeline.
"""

import pandas as pd
import numpy as np
import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from backtesting_rule_parser import BacktestingRuleParser

def test_orb_pattern_parsing():
    """Test that ORB patterns can be parsed and validated"""
    
    print("🧪 Testing ORB Pattern Parsing and Validation")
    print("=" * 60)
    
    # Create a simple ORB pattern
    orb_pattern = {
        "pattern_name": "Simple ORB Breakout",
        "description": "Opening Range Breakout pattern for testing",
        "market_situation": "London session opening range breakout",
        "entry_conditions": [
            {
                "condition": "orb_breakout_above",
                "orb_period_bars": 2,
                "session": "london"
            }
        ],
        "exit_conditions": [
            {
                "condition": "risk_reward_ratio",
                "risk": 1,
                "reward": 2
            }
        ],
        "position_sizing": {
            "method": "fixed_percent",
            "value": 0.01
        },
        "optimal_conditions": {
            "timeframes": ["5min"]
        }
    }
    
    print(f"📋 Testing ORB pattern:")
    print(f"   Entry: {orb_pattern['entry_conditions'][0]['condition']}")
    print(f"   ORB Period: {orb_pattern['entry_conditions'][0]['orb_period_bars']} bars")
    print(f"   Session: {orb_pattern['entry_conditions'][0]['session']}")
    
    # Initialize parser
    parser = BacktestingRuleParser()
    
    try:
        # Parse the pattern (convert to JSON string first)
        json_string = json.dumps(orb_pattern)
        parser.parse_llm_response(json_string)
        
        if parser.validation_errors:
            print(f"❌ Validation errors found:")
            for error in parser.validation_errors:
                print(f"   - {error}")
            return False
        else:
            print(f"✅ Pattern parsed successfully!")
            print(f"   Patterns created: {len(parser.patterns)}")
            
            if parser.patterns:
                pattern = parser.patterns[0]
                print(f"   Entry conditions: {len(pattern.entry_conditions)}")
                print(f"   Exit conditions: {len(pattern.exit_conditions)}")
                
                # Test that the condition is recognized
                entry_condition = pattern.entry_conditions[0]
                condition_name = entry_condition['condition']
                
                if condition_name in parser.supported_conditions:
                    print(f"✅ Condition '{condition_name}' is supported")
                    return True
                else:
                    print(f"❌ Condition '{condition_name}' is not supported")
                    return False
            else:
                print(f"❌ No patterns were created")
                return False
                
    except Exception as e:
        print(f"❌ Error parsing pattern: {e}")
        return False

def test_orb_condition_function():
    """Test the ORB condition function directly"""
    
    print(f"\n🔧 Testing ORB Condition Function Directly")
    print("-" * 40)
    
    # Create simple test data
    dates = pd.date_range('2024-01-15 08:00:00', periods=20, freq='5min')
    data = pd.DataFrame({
        'Open': np.random.uniform(18000, 18100, 20),
        'High': np.random.uniform(18050, 18150, 20),
        'Low': np.random.uniform(17950, 18050, 20),
        'Close': np.random.uniform(18000, 18100, 20),
        'Volume': np.random.randint(1000, 5000, 20)
    }, index=dates)
    
    # Ensure High >= max(Open, Close) and Low <= min(Open, Close)
    for i in range(len(data)):
        data.iloc[i, data.columns.get_loc('High')] = max(data.iloc[i]['Open'], data.iloc[i]['Close'], data.iloc[i]['High'])
        data.iloc[i, data.columns.get_loc('Low')] = min(data.iloc[i]['Open'], data.iloc[i]['Close'], data.iloc[i]['Low'])
    
    print(f"   Created test data: {len(data)} bars")
    
    parser = BacktestingRuleParser()
    
    # Test parameters
    params = {
        'orb_period_bars': 2,
        'session': 'london'
    }
    
    # Test at index 10
    test_idx = 10
    
    try:
        result = parser._orb_breakout_above(data, test_idx, params)
        print(f"   ORB breakout above at index {test_idx}: {result}")
        
        result = parser._orb_breakout_below(data, test_idx, params)
        print(f"   ORB breakout below at index {test_idx}: {result}")
        
        result = parser._opening_range_high(data, test_idx, params)
        print(f"   Opening range high available at index {test_idx}: {result}")
        
        print(f"✅ ORB condition functions work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing ORB condition: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ORB Integration Test")
    print("=" * 60)
    
    success1 = test_orb_pattern_parsing()
    success2 = test_orb_condition_function()
    
    overall_success = success1 and success2
    
    print(f"\n📊 Test Results:")
    print(f"   Pattern Parsing: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Condition Function: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"   Overall: {'✅ SUCCESS' if overall_success else '❌ FAILURE'}")
    
    if overall_success:
        print(f"\n🎉 ORB conditions are ready for backtesting!")
        print(f"   The system should now generate actual trading signals instead of zero signals.")
    else:
        print(f"\n💥 ORB conditions still have issues that need to be fixed.")
    
    sys.exit(0 if overall_success else 1)
